using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Users.DTOs;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace HWSAuditPlatform.Application.Users.Queries.GetUsers;

/// <summary>
/// Handler for GetUsersQuery
/// </summary>
public class GetUsersQueryHandler : BaseQueryHandler<GetUsersQuery, PaginatedResult<UserSummaryDto>>
{
    private readonly IApplicationDbContext _context;

    public GetUsersQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<PaginatedResult<UserSummaryDto>> Handle(GetUsersQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Users
            .Include(u => u.Role)
            .Include(u => u.Factory)
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(u => 
                u.Username.ToLower().Contains(searchTerm) ||
                u.FirstName.ToLower().Contains(searchTerm) ||
                u.LastName.ToLower().Contains(searchTerm) ||
                u.Email.ToLower().Contains(searchTerm));
        }

        if (request.Role.HasValue)
        {
            query = query.Where(u => u.Role.RoleName == request.Role.Value);
        }

        if (request.IsActive.HasValue)
        {
            query = query.Where(u => u.IsActive == request.IsActive.Value);
        }

        if (request.FactoryId.HasValue)
        {
            query = query.Where(u => u.FactoryId == request.FactoryId.Value);
        }

        // Apply sorting
        query = ApplySorting(query, request.SortBy, request.SortDescending);

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var users = await query
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(u => new UserSummaryDto
            {
                Id = u.Id,
                Username = u.Username,
                FullName = $"{u.FirstName} {u.LastName}".Trim(),
                Email = u.Email,
                Role = u.Role.RoleName,
                IsActive = u.IsActive,
                FactoryName = u.Factory != null ? u.Factory.FactoryName : null
            })
            .ToListAsync(cancellationToken);

        return PaginatedResult<UserSummaryDto>.Create(users, totalCount, request.PageNumber, request.PageSize);
    }

    private static IQueryable<Domain.Entities.Users.User> ApplySorting(
        IQueryable<Domain.Entities.Users.User> query, 
        string? sortBy, 
        bool sortDescending)
    {
        Expression<Func<Domain.Entities.Users.User, object>> keySelector = sortBy?.ToLower() switch
        {
            "firstname" => u => u.FirstName,
            "lastname" => u => u.LastName,
            "email" => u => u.Email,
            "role" => u => u.Role.RoleName,
            "isactive" => u => u.IsActive,
            "createdat" => u => u.CreatedAt,
            _ => u => u.Username
        };

        return sortDescending 
            ? query.OrderByDescending(keySelector)
            : query.OrderBy(keySelector);
    }
}
