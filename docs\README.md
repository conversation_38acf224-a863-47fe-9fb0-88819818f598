# HWS Audit Platform - Documentation

## Overview

The HWS Audit Platform is a comprehensive audit management system designed for manufacturing quality audits with offline capabilities, Active Directory integration, and enterprise-scale requirements. This documentation provides complete guidance for understanding, developing, and deploying the platform.

## Documentation Structure

### 📋 **Core Documentation**

#### [Architecture Overview](Architecture-Overview.md)
Comprehensive overview of the system architecture, design patterns, and technology stack.
- Clean Architecture principles
- Layer responsibilities and dependencies
- Technology stack and design patterns
- Security and scalability considerations
- Future architecture evolution

#### [Application Layer Documentation](Application-Layer-Documentation.md)
Detailed guide to the Application layer implementation.
- CQRS pattern with MediatR
- Commands, queries, and handlers
- Validation framework with FluentValidation
- DTOs and AutoMapper configurations
- Pipeline behaviors and cross-cutting concerns

#### [Infrastructure Layer Documentation](Infrastructure-Layer-Documentation.md)
Comprehensive guide to the Infrastructure layer.
- Entity Framework Core implementation
- File storage (Azure Blob Storage / Local)
- Active Directory integration
- Repository and Unit of Work patterns
- Health monitoring and configuration

#### [API Integration Guide](API-Integration-Guide.md)
Complete guide for integrating with the platform APIs.
- Controller implementations
- Request/response flows
- Authentication and authorization
- File upload/download operations
- Offline synchronization patterns

#### [Developer Guide](Developer-Guide.md)
Essential guide for developers working on the platform.
- Development environment setup
- Project structure and conventions
- Feature development workflow
- Database management and migrations
- Testing guidelines and best practices

## Quick Start

### Prerequisites
- .NET 10 SDK
- SQL Server (LocalDB, Express, or full version)
- Visual Studio 2024 or VS Code
- Git

### Setup Instructions
```bash
# 1. Clone the repository
git clone https://github.com/company/hws-audit-platform.git
cd hws-audit-platform

# 2. Restore packages
dotnet restore

# 3. Set up database
dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService

# 4. Run the application
dotnet run --project src/HWSAuditPlatform.ApiService
```

### Configuration
Update `appsettings.Development.json` with your local settings:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=HWSAuditPlatformDb_Dev;Trusted_Connection=true"
  },
  "FileStorage": {
    "Type": "Local"
  },
  "LocalFileStorage": {
    "StoragePath": "wwwroot/uploads"
  }
}
```

## Architecture Summary

### Clean Architecture Layers
```
┌─────────────────────────────────────┐
│        Presentation (API)           │  ← Controllers, HTTP handling
├─────────────────────────────────────┤
│         Application                 │  ← Use cases, CQRS, validation
├─────────────────────────────────────┤
│           Domain                    │  ← Business logic, entities
├─────────────────────────────────────┤
│       Infrastructure               │  ← Database, external services
└─────────────────────────────────────┘
```

### Key Features
- **CQRS with MediatR**: Clean separation of read/write operations
- **Entity Framework Core**: Advanced ORM with migrations and configurations
- **Offline Sync**: PWA support with CUID and conflict resolution
- **File Storage**: Flexible storage (Azure Blob Storage / Local)
- **Active Directory**: Enterprise user and group synchronization
- **Health Monitoring**: Comprehensive health checks and logging
- **Clean Architecture**: Testable, maintainable, and scalable design

## Technology Stack

### Backend
- **.NET 10**: Latest framework with performance improvements
- **ASP.NET Core**: Web API framework
- **Entity Framework Core 8**: Object-relational mapping
- **SQL Server**: Primary database
- **MediatR**: CQRS implementation
- **AutoMapper**: Object-object mapping
- **FluentValidation**: Input validation

### Infrastructure
- **Azure Blob Storage**: Cloud file storage
- **System.DirectoryServices**: Active Directory integration
- **Azure Application Insights**: Monitoring and telemetry
- **Serilog**: Structured logging

### Development
- **xUnit**: Unit testing framework
- **FluentAssertions**: Test assertions
- **Moq**: Mocking framework
- **Swagger/OpenAPI**: API documentation

## Project Structure

```
HWSAuditPlatform/
├── src/
│   ├── HWSAuditPlatform.Domain/          # Business entities and rules
│   ├── HWSAuditPlatform.Application/     # Use cases and orchestration
│   ├── HWSAuditPlatform.Infrastructure/  # External concerns
│   └── HWSAuditPlatform.ApiService/      # Web API controllers
├── tests/
│   ├── HWSAuditPlatform.Domain.Tests/
│   ├── HWSAuditPlatform.Application.Tests/
│   ├── HWSAuditPlatform.Infrastructure.Tests/
│   └── HWSAuditPlatform.Integration.Tests/
├── docs/                                 # Documentation
└── scripts/                             # Deployment scripts
```

## Key Concepts

### Domain-Driven Design
- **Entities**: Core business objects with identity
- **Value Objects**: Immutable objects without identity
- **Domain Events**: Business events that trigger side effects
- **Aggregates**: Consistency boundaries for related entities

### CQRS Pattern
- **Commands**: Modify application state (CreateUser, UpdateAudit)
- **Queries**: Return data without side effects (GetUsers, GetAudits)
- **Handlers**: Process commands and queries
- **DTOs**: Data transfer objects for API communication

### Offline Synchronization
- **CUID**: Client-generated unique identifiers
- **Optimistic Concurrency**: RecordVersion for conflict detection
- **Conflict Resolution**: Automatic and manual conflict handling
- **PWA Support**: Progressive Web App capabilities

## Development Workflow

### Adding New Features
1. **Domain First**: Create or modify domain entities
2. **Application Layer**: Add commands/queries, handlers, DTOs
3. **Infrastructure**: Add entity configurations, migrations
4. **API Layer**: Create controllers and endpoints
5. **Testing**: Unit tests, integration tests, validation

### Database Changes
1. Modify entity or add new entity
2. Add/update entity configuration
3. Create migration: `dotnet ef migrations add <MigrationName>`
4. Review generated migration
5. Update database: `dotnet ef database update`

### Testing Strategy
- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test complete workflows
- **API Tests**: Test HTTP endpoints and responses
- **Performance Tests**: Load testing for scalability

## Deployment

### Development
- Local SQL Server or LocalDB
- Local file storage
- Development Active Directory (optional)

### Production
- Azure SQL Database
- Azure Blob Storage
- Production Active Directory
- Azure App Service or Kubernetes
- Application Insights monitoring

## Security

### Authentication & Authorization
- JWT token-based authentication
- Role-based access control (Admin, Manager, Auditor)
- Claims-based authorization
- Active Directory integration

### Data Protection
- Encryption at rest and in transit
- Audit trails for all changes
- Data retention policies
- Secure file storage

## Monitoring & Observability

### Health Checks
- Database connectivity
- Active Directory connectivity
- File storage accessibility
- External service dependencies

### Logging
- Structured logging with Serilog
- Application Insights integration
- Performance monitoring
- Error tracking and alerting

## Support & Contributing

### Getting Help
- Review documentation thoroughly
- Check existing issues and solutions
- Follow coding standards and conventions
- Write comprehensive tests

### Contributing Guidelines
- Follow clean architecture principles
- Maintain test coverage above 80%
- Document new features and changes
- Follow Git workflow and PR process

## Related Resources

- **Domain Model**: See `src/HWSAuditPlatform.Domain/` for business entities
- **Database Schema**: See `docs/database.dbml` for complete schema
- **API Documentation**: Swagger UI available at `/swagger` endpoint
- **Technical Requirements**: See `docs/HWSAuditPlatform-TechnicalRequirementsSpecification.md`

---

This documentation provides comprehensive guidance for understanding, developing, and maintaining the HWS Audit Platform. For specific implementation details, refer to the individual layer documentation files.
