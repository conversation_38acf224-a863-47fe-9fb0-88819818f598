using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Users.DTOs;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Users.Queries.GetUser;

/// <summary>
/// Handler for GetUserQuery
/// </summary>
public class GetUserQueryHandler : BaseQueryHandler<GetUserQuery, UserDto>
{
    private readonly IApplicationDbContext _context;

    public GetUserQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<UserDto> Handle(GetUserQuery request, CancellationToken cancellationToken)
    {
        var user = await _context.Users
            .Include(u => u.Role)
            .Include(u => u.Factory)
            .FirstOrDefaultAsync(u => u.Id == request.Id, cancellationToken);

        if (user == null)
        {
            throw new NotFoundException(nameof(Domain.Entities.Users.User), request.Id);
        }

        return new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            FirstName = user.FirstName,
            LastName = user.LastName,
            Email = user.Email,
            Role = user.Role.RoleName,
            FactoryId = user.FactoryId,
            FactoryName = user.Factory?.FactoryName,
            IsActive = user.IsActive,
            LastLoginDate = user.LastLoginDate,
            AdObjectGuid = user.AdObjectGuid,
            AdDistinguishedName = user.AdDistinguishedName,
            AdSyncLastDate = user.AdSyncLastDate,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            RecordVersion = user.RecordVersion,
            CreatedByUserId = user.CreatedByUserId,
            UpdatedByUserId = user.UpdatedByUserId
        };
    }
}
