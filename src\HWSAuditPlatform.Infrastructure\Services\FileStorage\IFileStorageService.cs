namespace HWSAuditPlatform.Infrastructure.Services.FileStorage;

/// <summary>
/// Interface for file storage operations
/// </summary>
public interface IFileStorageService
{
    /// <summary>
    /// Uploads a file to storage
    /// </summary>
    /// <param name="fileName">The name of the file</param>
    /// <param name="contentType">The MIME type of the file</param>
    /// <param name="fileStream">The file content stream</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The storage path/URL of the uploaded file</returns>
    Task<string> UploadFileAsync(string fileName, string contentType, Stream fileStream, CancellationToken cancellationToken = default);

    /// <summary>
    /// Downloads a file from storage
    /// </summary>
    /// <param name="filePath">The storage path of the file</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The file content stream</returns>
    Task<Stream> DownloadFileAsync(string filePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a file from storage
    /// </summary>
    /// <param name="filePath">The storage path of the file</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task DeleteFileAsync(string filePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a file exists in storage
    /// </summary>
    /// <param name="filePath">The storage path of the file</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the file exists, false otherwise</returns>
    Task<bool> FileExistsAsync(string filePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the file size in bytes
    /// </summary>
    /// <param name="filePath">The storage path of the file</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The file size in bytes</returns>
    Task<long> GetFileSizeAsync(string filePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates a temporary download URL for a file
    /// </summary>
    /// <param name="filePath">The storage path of the file</param>
    /// <param name="expiryTime">How long the URL should be valid</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>A temporary download URL</returns>
    Task<string> GenerateDownloadUrlAsync(string filePath, TimeSpan expiryTime, CancellationToken cancellationToken = default);
}
