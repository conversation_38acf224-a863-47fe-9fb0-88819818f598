using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Domain.Entities.Users;

/// <summary>
/// Represents a user group for audit assignments.
/// Maps to the UserGroups table in the database.
/// </summary>
public class UserGroup : AuditableEntity<string>, IAggregateRoot
{
    /// <summary>
    /// Display name of the user group
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string GroupName { get; set; } = string.Empty;

    /// <summary>
    /// Optional description of the group's purpose
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// AD objectGUID for synced groups, or NULL for local groups
    /// </summary>
    [MaxLength(255)]
    public string? AdObjectGuid { get; set; }

    /// <summary>
    /// Indicates if the group is synced from AD
    /// </summary>
    public bool IsAdSynced { get; set; }

    /// <summary>
    /// Navigation property for group members
    /// </summary>
    public virtual ICollection<UserGroupMember> Members { get; set; } = new List<UserGroupMember>();
}
