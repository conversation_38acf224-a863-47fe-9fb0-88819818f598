using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Users.Commands.UpdateUser;

/// <summary>
/// Handler for UpdateUserCommand
/// </summary>
public class UpdateUserCommandHandler : BaseCommandHandler<UpdateUserCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public UpdateUserCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task Handle(UpdateUserCommand request, CancellationToken cancellationToken)
    {
        // Get the user
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Id == request.Id, cancellationToken);

        if (user == null)
        {
            throw new NotFoundException(nameof(Domain.Entities.Users.User), request.Id);
        }

        // Check for concurrency conflicts
        if (user.RecordVersion != request.RecordVersion)
        {
            throw new ConflictException("The user has been modified by another user. Please refresh and try again.");
        }

        // Get the role entity
        var role = await _context.Roles
            .FirstOrDefaultAsync(r => r.RoleName == request.Role, cancellationToken);

        if (role == null)
        {
            throw new InvalidOperationException($"Role '{request.Role}' not found");
        }

        // Update the user
        user.Update(
            firstName: request.FirstName,
            lastName: request.LastName,
            email: request.Email,
            roleId: role.Id,
            factoryId: request.FactoryId,
            isActive: request.IsActive,
            updatedByUserId: _currentUserService.UserId);

        await _context.SaveChangesAsync(cancellationToken);
    }
}
