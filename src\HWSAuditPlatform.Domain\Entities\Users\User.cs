using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Organization;

namespace HWSAuditPlatform.Domain.Entities.Users;

/// <summary>
/// Represents a user in the audit platform.
/// Maps to the Users table in the database.
/// </summary>
public class User : AuditableEntity<string>, IAggregateRoot
{
    /// <summary>
    /// Login username for the user
    /// </summary>
    [Required]
    [MaxLength(256)]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// User's first name
    /// </summary>
    [MaxLength(100)]
    public string? FirstName { get; set; }

    /// <summary>
    /// User's last name
    /// </summary>
    [MaxLength(100)]
    public string? LastName { get; set; }

    /// <summary>
    /// User's email address, used for notifications and potentially login
    /// </summary>
    [Required]
    [MaxLength(256)]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Foreign key linking to the user's primary role
    /// </summary>
    public int RoleId { get; set; }

    /// <summary>
    /// Navigation property for the user's role
    /// </summary>
    public virtual Role Role { get; set; } = null!;

    /// <summary>
    /// Optional: Primary factory association for the user
    /// </summary>
    public int? FactoryId { get; set; }

    /// <summary>
    /// Navigation property for the user's primary factory
    /// </summary>
    public virtual Factory? Factory { get; set; }

    /// <summary>
    /// Indicates if the user account is active or disabled
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Timestamp of the user's last successful login
    /// </summary>
    public DateTime? LastLoginDate { get; set; }

    /// <summary>
    /// AD objectGUID for synced users (required - all users are AD synced)
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string AdObjectGuid { get; set; } = string.Empty;

    /// <summary>
    /// Full AD distinguished name (optional, for debugging/tracing)
    /// </summary>
    public string? AdDistinguishedName { get; set; }

    /// <summary>
    /// Last time this user was synced from AD
    /// </summary>
    public DateTime? AdSyncLastDate { get; set; }

    /// <summary>
    /// Navigation property for user group memberships
    /// </summary>
    public virtual ICollection<UserGroupMember> UserGroupMemberships { get; set; } = new List<UserGroupMember>();

    /// <summary>
    /// Gets the user's full name
    /// </summary>
    public string FullName => $"{FirstName} {LastName}".Trim();

    /// <summary>
    /// Indicates if this user is synced from Active Directory (always true - all users are AD synced)
    /// </summary>
    public bool IsAdSynced => true;
}
