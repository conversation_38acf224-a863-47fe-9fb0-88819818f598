using HWSAuditPlatform.Application.DTOs;

namespace HWSAuditPlatform.Application.Users.DTOs;

/// <summary>
/// Data Transfer Object for UserGroup entity
/// </summary>
public class UserGroupDto : AuditableDto<string>
{
    public string GroupName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string CreatedByUserId { get; set; } = string.Empty;
    public string? CreatedByUserName { get; set; }
    public string? AdObjectGuid { get; set; }
    public bool IsAdSynced { get; set; }
    
    public List<UserSummaryDto> Members { get; set; } = new();
    public int MemberCount => Members.Count;
}

/// <summary>
/// Simplified UserGroup DTO for lists and lookups
/// </summary>
public class UserGroupSummaryDto
{
    public string Id { get; set; } = string.Empty;
    public string GroupName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int MemberCount { get; set; }
    public bool IsAdSynced { get; set; }
}
