using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HWSAuditPlatform.Domain.Entities.Users;

namespace HWSAuditPlatform.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for UserGroup entity
/// </summary>
public class UserGroupConfiguration : IEntityTypeConfiguration<UserGroup>
{
    public void Configure(EntityTypeBuilder<UserGroup> builder)
    {
        builder.ToTable("UserGroups");

        // Primary Key
        builder.HasKey(ug => ug.Id);
        builder.Property(ug => ug.Id)
            .HasMaxLength(25)
            .IsRequired();

        // Properties
        builder.Property(ug => ug.GroupName)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(ug => ug.Description)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(ug => ug.AdObjectGuid)
            .HasMaxLength(255)
            .IsRequired(false);

        builder.Property(ug => ug.IsAdSynced)
            .IsRequired()
            .HasDefaultValue(false);

        // Auditable properties
        builder.Property(ug => ug.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(ug => ug.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(ug => ug.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(ug => ug.CreatedByUserId)
            .HasMaxLength(25)
            .IsRequired();

        builder.Property(ug => ug.UpdatedByUserId)
            .HasMaxLength(25)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(ug => ug.GroupName)
            .IsUnique()
            .HasDatabaseName("IX_UserGroups_GroupName");

        builder.HasIndex(ug => ug.AdObjectGuid)
            .IsUnique()
            .HasDatabaseName("IX_UserGroups_AdObjectGuid")
            .HasFilter("[AdObjectGuid] IS NOT NULL");

        // Relationships
        builder.HasOne(ug => ug.CreatedByUser)
            .WithMany()
            .HasForeignKey(ug => ug.CreatedByUserId)
            .OnDelete(DeleteBehavior.Restrict);

        // Ignore domain events
        builder.Ignore(ug => ug.DomainEvents);
    }
}

/// <summary>
/// Entity Framework configuration for UserGroupMember entity
/// </summary>
public class UserGroupMemberConfiguration : IEntityTypeConfiguration<UserGroupMember>
{
    public void Configure(EntityTypeBuilder<UserGroupMember> builder)
    {
        builder.ToTable("UserGroupMembers");

        // Composite Primary Key
        builder.HasKey(ugm => new { ugm.UserGroupId, ugm.UserId });

        // Properties
        builder.Property(ugm => ugm.UserGroupId)
            .HasMaxLength(25)
            .IsRequired();

        builder.Property(ugm => ugm.UserId)
            .HasMaxLength(25)
            .IsRequired();

        // Relationships
        builder.HasOne(ugm => ugm.UserGroup)
            .WithMany(ug => ug.UserGroupMembers)
            .HasForeignKey(ugm => ugm.UserGroupId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ugm => ugm.User)
            .WithMany(u => u.UserGroupMembers)
            .HasForeignKey(ugm => ugm.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(ugm => ugm.UserGroupId)
            .HasDatabaseName("IX_UserGroupMembers_UserGroupId");

        builder.HasIndex(ugm => ugm.UserId)
            .HasDatabaseName("IX_UserGroupMembers_UserId");
    }
}
