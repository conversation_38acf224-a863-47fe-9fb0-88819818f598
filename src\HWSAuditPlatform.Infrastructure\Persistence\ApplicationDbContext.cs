using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Entities.Scheduling;
using HWSAuditPlatform.Domain.Entities.Workflow;
using HWSAuditPlatform.Domain.Common;
using System.Reflection;

namespace HWSAuditPlatform.Infrastructure.Persistence;

/// <summary>
/// Entity Framework Core database context for the HWS Audit Platform
/// </summary>
public class ApplicationDbContext : DbContext, IApplicationDbContext
{
    private readonly ICurrentUserService _currentUserService;

    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, ICurrentUserService currentUserService)
        : base(options)
    {
        _currentUserService = currentUserService;
    }

    // User Management
    public DbSet<Role> Roles => Set<Role>();
    public DbSet<User> Users => Set<User>();
    public DbSet<UserGroup> UserGroups => Set<UserGroup>();
    public DbSet<UserGroupMember> UserGroupMembers => Set<UserGroupMember>();
    public DbSet<AdGroupRoleMapping> AdGroupRoleMappings => Set<AdGroupRoleMapping>();

    // Organizational Structure
    public DbSet<Location> Locations => Set<Location>();
    public DbSet<Factory> Factories => Set<Factory>();
    public DbSet<Area> Areas => Set<Area>();
    public DbSet<SubArea> SubAreas => Set<SubArea>();

    // Audit Templates
    public DbSet<AuditTemplate> AuditTemplates => Set<AuditTemplate>();
    public DbSet<QuestionGroup> QuestionGroups => Set<QuestionGroup>();
    public DbSet<Question> Questions => Set<Question>();
    public DbSet<QuestionOption> QuestionOptions => Set<QuestionOption>();

    // Audit Execution
    public DbSet<Audit> Audits => Set<Audit>();
    public DbSet<AuditAnswer> AuditAnswers => Set<AuditAnswer>();
    public DbSet<AuditAnswerSelectedOption> AuditAnswerSelectedOptions => Set<AuditAnswerSelectedOption>();
    public DbSet<AuditAnswerFailureReason> AuditAnswerFailureReasons => Set<AuditAnswerFailureReason>();
    public DbSet<AuditAttachment> AuditAttachments => Set<AuditAttachment>();

    // Findings & Corrective Actions
    public DbSet<Finding> Findings => Set<Finding>();
    public DbSet<CorrectiveAction> CorrectiveActions => Set<CorrectiveAction>();

    // Scheduling
    public DbSet<RecurringAuditSetting> RecurringAuditSettings => Set<RecurringAuditSetting>();
    public DbSet<RecurrenceRule> RecurrenceRules => Set<RecurrenceRule>();

    // Workflow
    public DbSet<AuditCorrectionRequest> AuditCorrectionRequests => Set<AuditCorrectionRequest>();
    public DbSet<AuditLog> AuditLogs => Set<AuditLog>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Apply all entity configurations from the current assembly
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

        base.OnModelCreating(modelBuilder);
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Update audit fields before saving
        UpdateAuditFields();

        return await base.SaveChangesAsync(cancellationToken);
    }

    private void UpdateAuditFields()
    {
        var entries = ChangeTracker.Entries<BaseEntity>()
            .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

        var currentUserId = _currentUserService.UserId;
        var now = DateTime.UtcNow;

        foreach (var entry in entries)
        {
            if (entry.State == EntityState.Added)
            {
                entry.Entity.CreatedAt = now;
                entry.Entity.UpdatedAt = now;

                if (entry.Entity is AuditableEntity<string> auditableStringEntity)
                {
                    auditableStringEntity.CreatedByUserId = currentUserId;
                    auditableStringEntity.UpdatedByUserId = currentUserId;
                    auditableStringEntity.RecordVersion = 1;
                }
                else if (entry.Entity is AuditableEntity<int> auditableIntEntity)
                {
                    auditableIntEntity.CreatedByUserId = currentUserId;
                    auditableIntEntity.UpdatedByUserId = currentUserId;
                    auditableIntEntity.RecordVersion = 1;
                }
            }
            else if (entry.State == EntityState.Modified)
            {
                entry.Entity.UpdatedAt = now;

                if (entry.Entity is AuditableEntity<string> auditableStringEntity)
                {
                    auditableStringEntity.UpdatedByUserId = currentUserId;
                    auditableStringEntity.RecordVersion++;
                }
                else if (entry.Entity is AuditableEntity<int> auditableIntEntity)
                {
                    auditableIntEntity.UpdatedByUserId = currentUserId;
                    auditableIntEntity.RecordVersion++;
                }
            }
        }
    }
}
