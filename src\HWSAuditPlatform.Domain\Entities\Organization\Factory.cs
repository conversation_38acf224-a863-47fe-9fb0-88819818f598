using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Users;

namespace HWSAuditPlatform.Domain.Entities.Organization;

/// <summary>
/// Represents a factory or site where audits are conducted.
/// Maps to the Factories table in the database.
/// </summary>
public class Factory : AuditableEntity<int>, IAggregateRoot
{
    /// <summary>
    /// Name of the factory
    /// </summary>
    [Required]
    [MaxLength(150)]
    public string FactoryName { get; set; } = string.Empty;

    /// <summary>
    /// Denormalized list of processes. 
    /// TODO: Normalize to Processes and FactoryProcesses tables for querying and integrity.
    /// </summary>
    [MaxLength(2000)]
    public string? FactoryProcess { get; set; }

    /// <summary>
    /// Links to the geographical location of the factory
    /// </summary>
    public int LocationId { get; set; }

    /// <summary>
    /// Navigation property for the location
    /// </summary>
    public virtual Location Location { get; set; } = null!;

    /// <summary>
    /// Address line 1
    /// </summary>
    [MaxLength(255)]
    public string? AddressLine1 { get; set; }

    /// <summary>
    /// City
    /// </summary>
    [MaxLength(100)]
    public string? City { get; set; }

    /// <summary>
    /// Postal code
    /// </summary>
    [MaxLength(20)]
    public string? PostalCode { get; set; }

    /// <summary>
    /// Indicates if the factory is currently operational/relevant for audits
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Navigation property for areas within this factory
    /// </summary>
    public virtual ICollection<Area> Areas { get; set; } = new List<Area>();

    /// <summary>
    /// Navigation property for users primarily associated with this factory
    /// </summary>
    public virtual ICollection<User> Users { get; set; } = new List<User>();
}
