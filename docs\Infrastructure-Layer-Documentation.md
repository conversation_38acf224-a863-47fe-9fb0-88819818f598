# HWS Audit Platform - Infrastructure Layer Documentation

## Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Data Persistence](#data-persistence)
4. [File Storage](#file-storage)
5. [Active Directory Integration](#active-directory-integration)
6. [Identity Services](#identity-services)
7. [Health Monitoring](#health-monitoring)
8. [Configuration](#configuration)
9. [Database Schema](#database-schema)
10. [Deployment Guide](#deployment-guide)

## Overview

The Infrastructure layer implements the interfaces defined in the Application layer and provides concrete implementations for external concerns such as data persistence, file storage, Active Directory integration, and other infrastructure services.

### Key Responsibilities
- **Data Persistence**: Entity Framework Core implementation
- **File Storage**: Azure Blob Storage and Local File System
- **External Integration**: Active Directory synchronization
- **Identity Management**: User context and authentication
- **Health Monitoring**: System health checks
- **Event Publishing**: Domain event handling

### Design Principles
- **Interface Implementation**: Implements all Application layer interfaces
- **Configuration-Driven**: Behavior controlled through configuration
- **Environment-Aware**: Different implementations for dev/staging/production
- **Monitoring-Ready**: Built-in health checks and logging
- **Scalable**: Designed for enterprise-scale deployments

## Architecture

### Infrastructure Components
```
┌─────────────────────────────────────┐
│          Application Layer          │
│         (Interfaces)                │
├─────────────────────────────────────┤
│        Infrastructure Layer         │
│  ┌─────────────┬─────────────────┐  │
│  │ Persistence │  External       │  │
│  │ - EF Core   │  Services       │  │
│  │ - Repository│  - File Storage │  │
│  │ - UnitOfWork│  - Active Dir   │  │
│  └─────────────┴─────────────────┘  │
├─────────────────────────────────────┤
│         External Systems            │
│  ┌─────────────┬─────────────────┐  │
│  │ SQL Server  │  Azure Blob     │  │
│  │ Database    │  Storage        │  │
│  └─────────────┴─────────────────┘  │
└─────────────────────────────────────┘
```

### Key Technologies
- **Entity Framework Core 8.0**: Object-relational mapping
- **SQL Server**: Primary database
- **Azure Blob Storage**: Cloud file storage
- **System.DirectoryServices**: Active Directory integration
- **ASP.NET Core Identity**: Authentication support

## Data Persistence

### Entity Framework Core Implementation

**ApplicationDbContext**
The main database context implements `IApplicationDbContext` and provides:

```csharp
public class ApplicationDbContext : DbContext, IApplicationDbContext
{
    // DbSets for all entities
    public DbSet<User> Users => Set<User>();
    public DbSet<Audit> Audits => Set<Audit>();
    // ... other entities

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateAuditFields(); // Automatic audit tracking
        return await base.SaveChangesAsync(cancellationToken);
    }
}
```

**Features:**
- **Automatic Audit Tracking**: CreatedAt, UpdatedAt, CreatedByUserId, UpdatedByUserId
- **Optimistic Concurrency**: RecordVersion fields with conflict detection
- **Domain Event Integration**: Collects and publishes domain events
- **Configuration-Based**: Entity configurations in separate files

### Repository Pattern Implementation

**Generic Repository**
```csharp
public class Repository<TEntity, TKey> : IRepository<TEntity, TKey>
    where TEntity : BaseEntity<TKey>
    where TKey : IEquatable<TKey>
{
    public async Task<TEntity?> GetByIdAsync(TKey id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FindAsync(new object[] { id }, cancellationToken);
    }

    public async Task<(IReadOnlyList<TEntity> Items, int TotalCount)> GetPagedAsync(
        int pageNumber, int pageSize,
        Expression<Func<TEntity, bool>>? predicate = null,
        Expression<Func<TEntity, object>>? orderBy = null,
        bool ascending = true,
        CancellationToken cancellationToken = default)
    {
        // Implementation with filtering, sorting, and pagination
    }
}
```

**Features:**
- **Type-Safe Queries**: LINQ expression support
- **Pagination**: Built-in pagination with total count
- **Filtering**: Flexible predicate-based filtering
- **Sorting**: Expression-based sorting
- **Async Operations**: Full async/await support

### Unit of Work Pattern

**Transaction Management**
```csharp
public class UnitOfWork : IUnitOfWork
{
    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<IDbTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
        return new DbTransactionWrapper(transaction);
    }
}
```

**Features:**
- **Transaction Boundaries**: Explicit transaction control
- **Resource Management**: Proper disposal patterns
- **Error Handling**: Transaction rollback on exceptions

## File Storage

### Abstraction Layer
The file storage system provides a unified interface for different storage backends:

```csharp
public interface IFileStorageService
{
    Task<string> UploadFileAsync(string fileName, string contentType, Stream fileStream, CancellationToken cancellationToken = default);
    Task<Stream> DownloadFileAsync(string filePath, CancellationToken cancellationToken = default);
    Task DeleteFileAsync(string filePath, CancellationToken cancellationToken = default);
    Task<bool> FileExistsAsync(string filePath, CancellationToken cancellationToken = default);
    Task<string> GenerateDownloadUrlAsync(string filePath, TimeSpan expiryTime, CancellationToken cancellationToken = default);
}
```

### Azure Blob Storage Implementation

**Production-Ready Cloud Storage**
```csharp
public class AzureBlobStorageService : IFileStorageService
{
    public async Task<string> UploadFileAsync(string fileName, string contentType, Stream fileStream, CancellationToken cancellationToken = default)
    {
        var containerClient = await GetContainerClientAsync(cancellationToken);
        var blobName = GenerateBlobName(fileName); // Organized by date with unique IDs
        var blobClient = containerClient.GetBlobClient(blobName);

        await blobClient.UploadAsync(fileStream, new BlobUploadOptions
        {
            HttpHeaders = new BlobHttpHeaders { ContentType = contentType }
        }, cancellationToken);

        return blobName;
    }
}
```

**Features:**
- **Automatic Organization**: Files organized by date (yyyy/MM/dd)
- **Unique Naming**: Prevents filename conflicts
- **SAS URL Generation**: Temporary download URLs
- **Content Type Handling**: Proper MIME type support
- **Container Management**: Automatic container creation

### Local File Storage Implementation

**Development and Testing**
```csharp
public class LocalFileStorageService : IFileStorageService
{
    public async Task<string> UploadFileAsync(string fileName, string contentType, Stream fileStream, CancellationToken cancellationToken = default)
    {
        var filePath = GenerateFilePath(fileName);
        var fullPath = Path.Combine(_options.StoragePath, filePath);
        
        // Ensure directory exists
        var directory = Path.GetDirectoryName(fullPath);
        if (!Directory.Exists(directory))
            Directory.CreateDirectory(directory);

        using var fileStreamOutput = new FileStream(fullPath, FileMode.Create, FileAccess.Write);
        await fileStream.CopyToAsync(fileStreamOutput, cancellationToken);

        return filePath;
    }
}
```

**Features:**
- **Local File System**: Stores files on local disk
- **Directory Management**: Automatic directory creation
- **Development URLs**: Generates local download URLs
- **Same Interface**: Drop-in replacement for Azure storage

## Active Directory Integration

### AD Service Implementation

**User and Group Synchronization**
```csharp
public class ActiveDirectoryService : IActiveDirectoryService
{
    public async Task<IEnumerable<AdUser>> GetUsersAsync(CancellationToken cancellationToken = default)
    {
        using var directoryEntry = CreateDirectoryEntry();
        using var directorySearcher = new DirectorySearcher(directoryEntry)
        {
            Filter = "(&(objectClass=user)(objectCategory=person))",
            PropertiesToLoad = { "objectGUID", "sAMAccountName", "givenName", "sn", "mail", ... }
        };

        var searchResults = directorySearcher.FindAll();
        return searchResults.Cast<SearchResult>()
            .Select(MapSearchResultToAdUser)
            .Where(user => user != null);
    }
}
```

**Features:**
- **User Retrieval**: Get all users or specific users
- **Group Management**: Retrieve groups and memberships
- **Credential Validation**: Authenticate against AD
- **Connection Testing**: Health check capabilities
- **Attribute Mapping**: Map AD attributes to domain objects

### AD Data Models

**User Representation**
```csharp
public class AdUser
{
    public string ObjectGuid { get; set; }
    public string Username { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Email { get; set; }
    public string DistinguishedName { get; set; }
    public bool IsActive { get; set; }
    public List<string> MemberOf { get; set; }
    // Additional properties...
}
```

**Group Representation**
```csharp
public class AdGroup
{
    public string ObjectGuid { get; set; }
    public string Name { get; set; }
    public string DistinguishedName { get; set; }
    public string? Description { get; set; }
    public List<string> Members { get; set; }
    // Additional properties...
}
```

## Identity Services

### Current User Service

**HTTP Context Integration**
```csharp
public class CurrentUserService : ICurrentUserService
{
    public string? UserId => _httpContextAccessor.HttpContext?.User?.FindFirstValue(ClaimTypes.NameIdentifier);
    public string? Username => _httpContextAccessor.HttpContext?.User?.FindFirstValue(ClaimTypes.Name);
    public UserRole? Role => GetRoleFromClaims();
    public int? FactoryId => GetFactoryIdFromClaims();
    public bool IsAuthenticated => _httpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated ?? false;

    public bool HasRole(UserRole role) => Role == role;
    public bool HasAnyRole(params UserRole[] roles) => Role != null && roles.Contains(Role.Value);
}
```

**Features:**
- **Claims-Based**: Extracts information from JWT claims
- **Role Support**: Role-based authorization helpers
- **Factory Association**: User's primary factory context
- **Authentication State**: Check if user is authenticated

## Health Monitoring

### Health Check Implementation

**Database Health**
```csharp
services.AddHealthChecks()
    .AddDbContext<ApplicationDbContext>("database")
    .AddCheck<ActiveDirectoryHealthCheck>("activedirectory")
    .AddCheck<FileStorageHealthCheck>("filestorage");
```

**Active Directory Health**
```csharp
public class ActiveDirectoryHealthCheck : IHealthCheck
{
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var isConnected = await _activeDirectoryService.TestConnectionAsync(cancellationToken);
            return isConnected 
                ? HealthCheckResult.Healthy("Active Directory connection is working")
                : HealthCheckResult.Unhealthy("Unable to connect to Active Directory");
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy("Active Directory health check failed", ex);
        }
    }
}
```

**File Storage Health**
```csharp
public class FileStorageHealthCheck : IHealthCheck
{
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            // Test storage accessibility
            var testFileName = "health-check-test.txt";
            await _fileStorageService.FileExistsAsync(testFileName, cancellationToken);
            return HealthCheckResult.Healthy("File storage service is accessible");
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy("File storage service is not accessible", ex);
        }
    }
}
```

## Configuration

### Database Configuration

**Connection String**
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=HWSAuditPlatformDb;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"
  }
}
```

**Entity Framework Options**
```csharp
services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(
        configuration.GetConnectionString("DefaultConnection"),
        b => b.MigrationsAssembly(typeof(ApplicationDbContext).Assembly.FullName)));
```

### File Storage Configuration

**Azure Blob Storage**
```json
{
  "FileStorage": {
    "Type": "Azure"
  },
  "ConnectionStrings": {
    "AzureBlobStorage": "DefaultEndpointsProtocol=https;AccountName=hwsaudit;AccountKey=...;EndpointSuffix=core.windows.net"
  },
  "AzureBlobStorage": {
    "ContainerName": "audit-attachments"
  }
}
```

**Local File Storage**
```json
{
  "FileStorage": {
    "Type": "Local"
  },
  "LocalFileStorage": {
    "StoragePath": "wwwroot/uploads",
    "BaseUrl": "https://localhost:5001"
  }
}
```

### Active Directory Configuration

**LDAP Settings**
```json
{
  "ActiveDirectory": {
    "Domain": "company.local",
    "Username": "<EMAIL>",
    "Password": "service-password",
    "SearchBase": "OU=Users,DC=company,DC=local",
    "UseSSL": true,
    "Port": 636,
    "TimeoutSeconds": 30
  }
}
```

### Service Registration

**Complete DI Setup**
```csharp
public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
{
    // Database
    services.AddDbContext<ApplicationDbContext>(options => ...);
    services.AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<ApplicationDbContext>());

    // Repositories and Unit of Work
    services.AddScoped<IUnitOfWork, UnitOfWork>();
    services.AddScoped(typeof(IRepository<,>), typeof(Repository<,>));

    // Identity Services
    services.AddHttpContextAccessor();
    services.AddScoped<ICurrentUserService, CurrentUserService>();

    // File Storage (configurable)
    AddFileStorage(services, configuration);

    // Active Directory
    services.AddScoped<IActiveDirectoryService, ActiveDirectoryService>();

    // Health Checks
    services.AddScoped<ActiveDirectoryHealthCheck>();
    services.AddScoped<FileStorageHealthCheck>();

    return services;
}
```

## Database Schema

### Entity Configurations

**User Entity Configuration**
```csharp
public class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.ToTable("Users");
        
        // Primary Key (CUID for offline sync)
        builder.HasKey(u => u.Id);
        builder.Property(u => u.Id).HasMaxLength(25).IsRequired();

        // Properties with constraints
        builder.Property(u => u.Username).HasMaxLength(256).IsRequired();
        builder.Property(u => u.Email).HasMaxLength(256).IsRequired();

        // Unique constraints
        builder.HasIndex(u => u.Username).IsUnique();
        builder.HasIndex(u => u.Email).IsUnique();

        // Relationships
        builder.HasOne(u => u.Role).WithMany().HasForeignKey(u => u.RoleId);
        builder.HasOne(u => u.Factory).WithMany().HasForeignKey(u => u.FactoryId);

        // Audit fields
        builder.Property(u => u.RecordVersion).IsConcurrencyToken();
    }
}
```

### Key Schema Features

**Primary Key Strategy**
- **CUID**: For entities created offline (Audit, AuditAnswer, User, etc.)
- **Integer**: For server-managed entities (AuditTemplate, Question, etc.)

**Audit Tracking**
- **CreatedAt/UpdatedAt**: Automatic timestamps
- **CreatedByUserId/UpdatedByUserId**: User tracking
- **RecordVersion**: Optimistic concurrency control

**Relationships**
- **Cascade Behaviors**: Appropriate delete behaviors
- **Foreign Keys**: Proper referential integrity
- **Indexes**: Optimized for common queries

### Migration Commands

**Create Migration**
```bash
dotnet ef migrations add InitialCreate --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService
```

**Update Database**
```bash
dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService
```

**Generate SQL Script**
```bash
dotnet ef migrations script --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService
```

## Deployment Guide

### Development Environment

**Prerequisites**
- SQL Server LocalDB or SQL Server Express
- .NET 10 SDK
- Visual Studio 2024 or VS Code

**Setup Steps**
1. Clone repository
2. Update connection strings in `appsettings.Development.json`
3. Run migrations: `dotnet ef database update`
4. Configure file storage (Local for development)
5. Configure AD settings (optional for development)

### Production Environment

**Prerequisites**
- SQL Server (Azure SQL Database recommended)
- Azure Blob Storage account
- Active Directory domain
- Application hosting (Azure App Service, IIS, etc.)

**Configuration**
1. **Database**: Azure SQL Database with appropriate service tier
2. **File Storage**: Azure Blob Storage with appropriate redundancy
3. **Active Directory**: Service account with read permissions
4. **Secrets Management**: Azure Key Vault for sensitive configuration
5. **Monitoring**: Application Insights for logging and monitoring

**Security Considerations**
- Use managed identities where possible
- Store connection strings in Azure Key Vault
- Enable SQL Database firewall rules
- Configure Blob Storage access policies
- Use HTTPS for all communications

This documentation provides comprehensive guidance for understanding, configuring, and deploying the Infrastructure layer of the HWS Audit Platform.
