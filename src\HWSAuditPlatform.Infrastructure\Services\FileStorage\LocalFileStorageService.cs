using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace HWSAuditPlatform.Infrastructure.Services.FileStorage;

/// <summary>
/// Local file system implementation of file storage service
/// </summary>
public class LocalFileStorageService : IFileStorageService
{
    private readonly ILogger<LocalFileStorageService> _logger;
    private readonly LocalFileStorageOptions _options;

    public LocalFileStorageService(
        IOptions<LocalFileStorageOptions> options,
        ILogger<LocalFileStorageService> logger)
    {
        _options = options.Value;
        _logger = logger;

        // Ensure the storage directory exists
        if (!Directory.Exists(_options.StoragePath))
        {
            Directory.CreateDirectory(_options.StoragePath);
        }
    }

    public async Task<string> UploadFileAsync(string fileName, string contentType, Stream fileStream, CancellationToken cancellationToken = default)
    {
        try
        {
            var filePath = GenerateFilePath(fileName);
            var fullPath = Path.Combine(_options.StoragePath, filePath);
            var directory = Path.GetDirectoryName(fullPath);

            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            using var fileStreamOutput = new FileStream(fullPath, FileMode.Create, FileAccess.Write);
            await fileStream.CopyToAsync(fileStreamOutput, cancellationToken);

            _logger.LogInformation("Successfully uploaded file {FileName} to {FilePath}", fileName, filePath);
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading file {FileName}", fileName);
            throw;
        }
    }

    public async Task<Stream> DownloadFileAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_options.StoragePath, filePath);
            
            if (!File.Exists(fullPath))
            {
                throw new FileNotFoundException($"File not found: {filePath}");
            }

            var fileBytes = await File.ReadAllBytesAsync(fullPath, cancellationToken);
            return new MemoryStream(fileBytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading file {FilePath}", filePath);
            throw;
        }
    }

    public Task DeleteFileAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_options.StoragePath, filePath);
            
            if (File.Exists(fullPath))
            {
                File.Delete(fullPath);
                _logger.LogInformation("Successfully deleted file {FilePath}", filePath);
            }

            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file {FilePath}", filePath);
            throw;
        }
    }

    public Task<bool> FileExistsAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_options.StoragePath, filePath);
            return Task.FromResult(File.Exists(fullPath));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if file exists {FilePath}", filePath);
            throw;
        }
    }

    public Task<long> GetFileSizeAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_options.StoragePath, filePath);
            
            if (!File.Exists(fullPath))
            {
                throw new FileNotFoundException($"File not found: {filePath}");
            }

            var fileInfo = new FileInfo(fullPath);
            return Task.FromResult(fileInfo.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting file size {FilePath}", filePath);
            throw;
        }
    }

    public Task<string> GenerateDownloadUrlAsync(string filePath, TimeSpan expiryTime, CancellationToken cancellationToken = default)
    {
        // For local storage, we return the file path as the URL
        // In a real implementation, you might generate a signed URL or token
        var url = $"{_options.BaseUrl?.TrimEnd('/')}/files/{filePath}";
        return Task.FromResult(url);
    }

    private static string GenerateFilePath(string fileName)
    {
        var timestamp = DateTime.UtcNow.ToString("yyyy/MM/dd");
        var uniqueId = Guid.NewGuid().ToString("N")[..8];
        var extension = Path.GetExtension(fileName);
        var nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
        
        return Path.Combine(timestamp, $"{nameWithoutExtension}_{uniqueId}{extension}");
    }
}

/// <summary>
/// Configuration options for Local File Storage
/// </summary>
public class LocalFileStorageOptions
{
    public const string SectionName = "LocalFileStorage";

    public string StoragePath { get; set; } = "wwwroot/uploads";
    public string? BaseUrl { get; set; }
}
