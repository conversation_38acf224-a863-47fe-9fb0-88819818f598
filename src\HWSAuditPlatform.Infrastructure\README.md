# HWS Audit Platform - Infrastructure Layer

## Overview

The Infrastructure layer implements the interfaces defined in the Application layer and provides concrete implementations for external concerns such as data persistence, file storage, Active Directory integration, and other infrastructure services.

## Architecture

### Key Technologies

- **Entity Framework Core**: Object-relational mapping and database access
- **SQL Server**: Primary database for audit data
- **Azure Blob Storage / Local File Storage**: File storage for audit attachments
- **System.DirectoryServices**: Active Directory integration
- **ASP.NET Core Identity**: Authentication and authorization support

### Project Structure

```
src/HWSAuditPlatform.Infrastructure/
├── Persistence/                   # Data access layer
│   ├── ApplicationDbContext.cs   # EF Core DbContext implementation
│   ├── UnitOfWork.cs             # Unit of Work pattern implementation
│   └── Configurations/           # Entity Framework configurations
│       ├── UserConfiguration.cs  # User entity configuration
│       ├── RoleConfiguration.cs  # Role entity configuration
│       └── OrganizationConfiguration.cs # Organization entities
├── Repositories/                  # Repository implementations
│   └── Repository.cs             # Generic repository implementation
├── Identity/                      # Identity and authentication
│   └── CurrentUserService.cs     # Current user context service
├── Services/                      # Infrastructure services
│   ├── DomainEventService.cs     # Domain event publishing
│   ├── FileStorage/              # File storage implementations
│   │   ├── IFileStorageService.cs # File storage interface
│   │   ├── AzureBlobStorageService.cs # Azure Blob Storage
│   │   └── LocalFileStorageService.cs # Local file system
│   └── ActiveDirectory/          # Active Directory integration
│       ├── IActiveDirectoryService.cs # AD service interface
│       ├── ActiveDirectoryService.cs  # AD service implementation
│       └── Models/               # AD data models
│           └── AdUser.cs         # AD user and group models
└── DependencyInjection.cs        # Service registration
```

## Key Features

### 1. Data Persistence

**Entity Framework Core DbContext**:
- Implements `IApplicationDbContext` from Application layer
- Automatic audit field updates (CreatedAt, UpdatedAt, CreatedByUserId, etc.)
- Optimistic concurrency control with RecordVersion
- Domain event handling integration

**Repository Pattern**:
- Generic repository implementation for all entities
- Support for complex queries with filtering, sorting, and pagination
- Async/await pattern throughout
- LINQ expression support for type-safe queries

**Unit of Work**:
- Transaction management across multiple repositories
- Ensures data consistency for complex operations
- Proper disposal and resource management

### 2. File Storage

**Flexible Storage Options**:
- **Azure Blob Storage**: Production-ready cloud storage
- **Local File Storage**: Development and testing scenarios
- Configurable via appsettings.json

**Features**:
- Secure file upload/download
- Automatic file organization by date
- Unique file naming to prevent conflicts
- Temporary download URL generation
- File existence and size checking

### 3. Active Directory Integration

**User Synchronization**:
- Retrieve users and groups from AD
- Map AD attributes to domain entities
- Support for nested group memberships
- Credential validation

**Features**:
- LDAP/LDAPS connectivity
- Configurable search base and filters
- Error handling and logging
- Connection testing capabilities

### 4. Identity Services

**Current User Context**:
- Extract user information from HTTP context
- Support for claims-based authentication
- Role-based authorization helpers
- Factory association tracking

## Configuration

### Database Connection

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=HWSAuditPlatformDb;Trusted_Connection=true;MultipleActiveResultSets=true"
  }
}
```

### File Storage Configuration

**Azure Blob Storage**:
```json
{
  "FileStorage": {
    "Type": "Azure"
  },
  "ConnectionStrings": {
    "AzureBlobStorage": "DefaultEndpointsProtocol=https;AccountName=...;AccountKey=...;EndpointSuffix=core.windows.net"
  },
  "AzureBlobStorage": {
    "ContainerName": "audit-attachments"
  }
}
```

**Local File Storage**:
```json
{
  "FileStorage": {
    "Type": "Local"
  },
  "LocalFileStorage": {
    "StoragePath": "wwwroot/uploads",
    "BaseUrl": "https://localhost:5001"
  }
}
```

### Active Directory Configuration

```json
{
  "ActiveDirectory": {
    "Domain": "company.local",
    "Username": "<EMAIL>",
    "Password": "service-password",
    "SearchBase": "OU=Users,DC=company,DC=local",
    "UseSSL": true,
    "Port": 636,
    "TimeoutSeconds": 30
  }
}
```

## Entity Framework Configurations

### User Entity Configuration

- **Primary Key**: CUID (varchar(25)) for offline sync support
- **Unique Constraints**: Username, Email, AdObjectGuid
- **Indexes**: Optimized for common query patterns
- **Relationships**: Role, Factory, UserGroups
- **Audit Fields**: CreatedBy, UpdatedBy, RecordVersion

### Organization Hierarchy

- **Location → Factory → Area → SubArea**
- **Address Value Object**: Embedded in Factory entity
- **Soft Delete**: IsActive flags for logical deletion
- **Audit Tracking**: Full audit trail for all changes

### Audit Entities

- **CUID Primary Keys**: For entities created offline (Audit, AuditAnswer, etc.)
- **Integer Primary Keys**: For server-managed entities (AuditTemplate, Question, etc.)
- **Optimistic Concurrency**: RecordVersion for conflict resolution
- **Complex Relationships**: Support for conditional questions, multi-select answers

## Usage Examples

### Registering Services

```csharp
public void ConfigureServices(IServiceCollection services)
{
    services.AddInfrastructure(Configuration);
}
```

### Using Repositories

```csharp
public class UserService
{
    private readonly IRepository<User, string> _userRepository;
    private readonly IUnitOfWork _unitOfWork;

    public async Task<User> CreateUserAsync(CreateUserRequest request)
    {
        var user = User.Create(request.Username, request.Email, ...);
        await _userRepository.AddAsync(user);
        await _unitOfWork.SaveChangesAsync();
        return user;
    }
}
```

### File Storage Operations

```csharp
public class AttachmentService
{
    private readonly IFileStorageService _fileStorage;

    public async Task<string> UploadAttachmentAsync(IFormFile file)
    {
        using var stream = file.OpenReadStream();
        return await _fileStorage.UploadFileAsync(
            file.FileName, 
            file.ContentType, 
            stream);
    }
}
```

### Active Directory Integration

```csharp
public class AdSyncService
{
    private readonly IActiveDirectoryService _adService;

    public async Task SyncUsersAsync()
    {
        var adUsers = await _adService.GetUsersAsync();
        // Process and sync users to database
    }
}
```

## Database Migrations

### Creating Migrations

```bash
# Add new migration
dotnet ef migrations add InitialCreate --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService

# Update database
dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService
```

### Seed Data

The infrastructure layer includes seed data for:
- **Roles**: Admin, Manager, Auditor roles
- **System Users**: Default admin user (if configured)
- **Sample Data**: Development/testing data (optional)

## Security Considerations

### Data Protection

- **Connection Strings**: Store securely (Azure Key Vault, User Secrets)
- **AD Credentials**: Use service accounts with minimal permissions
- **File Storage**: Implement proper access controls
- **Audit Logging**: Track all data access and modifications

### Performance Optimization

- **Database Indexes**: Optimized for common query patterns
- **Connection Pooling**: EF Core connection pooling enabled
- **Async Operations**: All database operations are async
- **Pagination**: Built-in pagination support for large datasets

## Testing

### Integration Tests

```csharp
public class UserRepositoryTests : IClassFixture<DatabaseFixture>
{
    [Fact]
    public async Task CreateUser_ShouldPersistToDatabase()
    {
        // Arrange
        var user = User.Create("testuser", "<EMAIL>", ...);
        
        // Act
        await _repository.AddAsync(user);
        await _unitOfWork.SaveChangesAsync();
        
        // Assert
        var savedUser = await _repository.GetByIdAsync(user.Id);
        Assert.NotNull(savedUser);
    }
}
```

### Mock Services

For unit testing, mock implementations are available:
- `MockFileStorageService`
- `MockActiveDirectoryService`
- `MockCurrentUserService`

## Monitoring and Logging

### Structured Logging

- **Entity Framework**: SQL query logging
- **File Operations**: Upload/download tracking
- **AD Operations**: Sync status and errors
- **Performance**: Query execution times

### Health Checks

```csharp
services.AddHealthChecks()
    .AddDbContext<ApplicationDbContext>()
    .AddCheck<ActiveDirectoryHealthCheck>("ad")
    .AddCheck<FileStorageHealthCheck>("filestorage");
```

## Future Enhancements

- **Caching**: Redis cache for frequently accessed data
- **Event Sourcing**: Complete audit trail with event store
- **Multi-tenancy**: Support for multiple organizations
- **Background Jobs**: Hangfire for scheduled tasks
- **Message Queues**: Service Bus for async processing
